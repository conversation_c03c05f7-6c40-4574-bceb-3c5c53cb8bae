/* (c) https://github.com/MontiCore/monticore */
package cd2gui.data;

/**
 * Represents a metric that can be displayed in the metrics GUI page.
 * This class encapsulates different types of metrics including simple values,
 * percentages, and chart-based visualizations.
 */
public class CD2GUIMetric {
    
    private String name;
    private String displayName;
    private String description;
    private String unit;
    private String color;
    private MetricType type;
    private Object value;
    private String valueExpression;
    private Object chartData;
    private String chartType;
    private Integer innerRadius;
    
    public enum MetricType {
        NUMERIC,
        PERCENTAGE, 
        CHART,
        TEXT
    }
    
    public CD2GUIMetric(String name, String displayName, MetricType type) {
        this.name = name;
        this.displayName = displayName;
        this.type = type;
    }
    
    // Getters and setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDisplayName() {
        return displayName != null ? displayName : name;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public MetricType getType() {
        return type;
    }
    
    public void setType(MetricType type) {
        this.type = type;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    public String getValueExpression() {
        return valueExpression;
    }
    
    public void setValueExpression(String valueExpression) {
        this.valueExpression = valueExpression;
    }
    
    public Object getChartData() {
        return chartData;
    }
    
    public void setChartData(Object chartData) {
        this.chartData = chartData;
    }
    
    public String getChartType() {
        return chartType;
    }
    
    public void setChartType(String chartType) {
        this.chartType = chartType;
    }
    
    public Integer getInnerRadius() {
        return innerRadius;
    }
    
    public void setInnerRadius(Integer innerRadius) {
        this.innerRadius = innerRadius;
    }
    
    public boolean hasChartData() {
        return chartData != null;
    }
    
    // Builder pattern for easier construction
    public static class Builder {
        private CD2GUIMetric metric;
        
        public Builder(String name, String displayName, MetricType type) {
            metric = new CD2GUIMetric(name, displayName, type);
        }
        
        public Builder description(String description) {
            metric.setDescription(description);
            return this;
        }
        
        public Builder unit(String unit) {
            metric.setUnit(unit);
            return this;
        }
        
        public Builder color(String color) {
            metric.setColor(color);
            return this;
        }
        
        public Builder value(Object value) {
            metric.setValue(value);
            return this;
        }
        
        public Builder valueExpression(String valueExpression) {
            metric.setValueExpression(valueExpression);
            return this;
        }
        
        public Builder chartData(Object chartData) {
            metric.setChartData(chartData);
            return this;
        }
        
        public Builder chartType(String chartType) {
            metric.setChartType(chartType);
            return this;
        }
        
        public Builder innerRadius(Integer innerRadius) {
            metric.setInnerRadius(innerRadius);
            return this;
        }
        
        public CD2GUIMetric build() {
            return metric;
        }
    }
}
