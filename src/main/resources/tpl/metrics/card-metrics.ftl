<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metrics")}

${name?uncap_first}MetricsCard@GemCard(
  title = "${name} Metrics",
  component = ${name?uncap_first}MetricsColumn@GemColumn(rowGap = "10px", components = [
    
    <#-- Navigation back to overview and details -->
    ${name?uncap_first}MetricsNavRow@GemRow(hAlign = "space-between", components = [
      <#if !attrManager.isOption("NoOverview")>
        ${name?uncap_first}Metrics_ToOverview@GemNavItem(target = "/${linkPath}/${name}Overview", title = "To Overview"),
      </#if>
      <#if !attrManager.isOption("NoDetails")>
        ${name?uncap_first}Metrics_ToDetails@GemNavItem(target = "/${linkPath}/${name}Details/" + ${name?lower_case}.gemId, title = "To Details"),
      </#if>
      <#if !attrManager.isOption("NoDashboard")>
        ${name?uncap_first}Metrics_ToDashboard@GemNavItem(target = "/${linkPath}/CD2GUIDashboard", title = "To Dashboard")
      </#if>
    ]),

    <#-- Metrics Grid Layout -->
    ${name?uncap_first}MetricsGrid@GemGrid(
      columnGap = "15px",
      rowGap = "15px", 
      rows = [
        <#-- Group metrics into rows of 2-3 items each -->
        <#assign metricsPerRow = 2>
        <#assign currentRow = []>
        <#list metrics as metric>
          <#assign currentRow = currentRow + [metric]>
          <#if (metric?index + 1) % metricsPerRow == 0 || !metric?has_next>
            [
              <#list currentRow as rowMetric>
                ${tc.includeArgs("metric-item", [domainClass, name, rowMetric])}<#sep>,</#sep>
              </#list>
            ]<#sep>,</#sep>
            <#assign currentRow = []>
          </#if>
        </#list>
      ]
    )
  ])
);
