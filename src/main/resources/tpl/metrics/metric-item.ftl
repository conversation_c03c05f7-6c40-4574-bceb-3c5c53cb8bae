<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metric")}

<#--
  Renders a single metric item as a card
  Flexible template that adapts to different metric properties
-->

<#-- Prepare the content based on metric type -->
<#if metric.hasChartData()!false>
  <#-- Chart-based metric -->
  <#if metric.getChartType()! == "PIE">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.pie-chart", [metric.getChartData(), metric.getInnerRadius()!40])}</#assign>
  <#elseif metric.getChartType()! == "BAR">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.bar-chart", [metric.getChartData(), "100%", "150px"])}</#assign>
  <#elseif metric.getChartType()! == "LINE">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.line-chart", [metric.getChartData(), "100%", "150px"])}</#assign>
  <#else>
    <#-- Default chart type -->
    <#assign metricContent>${tc.includeArgs("tpl.metrics.pie-chart", [metric.getChartData(), 40])}</#assign>
  </#if>
<#else>
  <#-- Simple value metric -->
  <#assign metricContent>
    @GemText(
      value = ${metric.getValueExpression()!"\"" + metric.getValue() + "\""},
      fontSize = "28px",
      fontWeight = "bold",
      color = "${metric.getColor()!"#2563eb"}"
    )<#if metric.getUnit()??>,
    @GemText(
      value = "${metric.getUnit()}",
      fontSize = "14px",
      color = "#6b7280"
    )</#if>
  </#assign>
</#if>

<#-- Render the metric using the metric-card template -->
${tc.includeArgs("tpl.metrics.metric-card", [
  metric.getDisplayName()!metric.getName(),
  metricContent,
  "40%",
  "200px",
  metric.getDescription()!""
])}
