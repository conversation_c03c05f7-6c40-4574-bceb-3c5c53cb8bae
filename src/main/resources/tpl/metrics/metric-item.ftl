<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metric")}

<#--
  Renders a single metric item as a card with appropriate visualization
  This is a flexible template that can handle different metric types
-->

<#-- Basic metric card structure -->
${name?uncap_first}_${metric.name}Card@GemCard(
  width = "100%",
  title = "${metric.displayName!metric.name}",
  component = ${name?uncap_first}_${metric.name}Content@GemColumn(
    hAlign = "center",
    vAlign = "center",
    rowGap = "10px",
    components = [

      <#-- Main metric value display -->
      <#if metric.chartData??>
        <#-- If metric has chart data, render appropriate chart -->
        <#if metric.chartType?? && metric.chartType == "PIE">
          ${tc.includeArgs("tpl.charts.pie-chart", [metric.chartData, metric.innerRadius!50])}
        <#elseif metric.chartType?? && metric.chartType == "BAR">
          @GemBarChart(data = ${metric.chartData})
        <#elseif metric.chartType?? && metric.chartType == "LINE">
          @GemLineChart(data = ${metric.chartData})
        <#else>
          <#-- Default to pie chart if chart type not specified -->
          ${tc.includeArgs("tpl.charts.pie-chart", [metric.chartData, 50])}
        </#if>
      <#else>
        <#-- Simple numeric/text metric -->
        ${name?uncap_first}_${metric.name}Value@GemText(
          value = ${metric.valueExpression!"metric.value"},
          fontSize = "24px",
          fontWeight = "bold"
        )
      </#if>,

      <#-- Optional unit display -->
      <#if metric.unit??>
        ${name?uncap_first}_${metric.name}Unit@GemText(
          value = "${metric.unit}",
          fontSize = "14px",
          color = "#666"
        ),
      </#if>

      <#-- Optional description -->
      <#if metric.description??>
        ${name?uncap_first}_${metric.name}Desc@GemText(
          value = "${metric.description}",
          fontSize = "12px",
          color = "#999"
        ),
      </#if>

      <#-- Navigation back to details if needed -->
      <#if !attrManager.isOption("NoDetails")>
        ${name?uncap_first}_${metric.name}BackToDetails@GemNavItem(
          target = "/${linkPath}/${name}Details/" + ${name?lower_case}.gemId,
          title = "Back to Details"
        )
      </#if>
    ]
  )
)
