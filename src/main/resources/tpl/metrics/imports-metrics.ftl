<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metrics")}
import mc.fenix.arrange.GemCard;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemGrid;
import mc.fenix.basic.GemText;
import mc.fenix.basic.GemButton;
import mc.fenix.navigation.GemNavItem;
import mc.fenix.charts.GemPieChart;
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemLineChart;
import mc.fenix.charts.GemPieChartTypes.GemPieChartData;
import mc.fenix.charts.GemPieChartTypes.GemPieChartEntry;
import mc.fenix.charts.GemBarChartTypes.GemBarChartData;
import mc.fenix.charts.GemBarChartTypes.GemBarChartEntry;
import mc.fenix.charts.GemLineChartTypes.GemLineChartData;
import mc.fenix.charts.GemLineChartTypes.GemLineChartEntry;
