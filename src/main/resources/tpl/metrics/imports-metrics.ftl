<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metrics")}

import mc.fenix.arrange.GemCard;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemGrid;
import mc.fenix.basic.GemText;
import mc.fenix.basic.GemButton;
import mc.fenix.navigation.GemNavItem;

import charts.GemBarChart;
import charts.GemBarChartTypes.GemBarChartData;

