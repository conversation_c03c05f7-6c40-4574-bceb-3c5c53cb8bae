${tc.signature(value, min, max, unit, color)}

<#--
gauge-chart.ftl for metrics
Creates a gauge/meter chart for displaying single values with min/max ranges.

Parameters
- value : Number                    → Current value to display
- min   : Number (optional)         → Minimum value (default: 0)
- max   : Number (optional)         → Maximum value (default: 100)
- unit  : String (optional)         → Unit to display (e.g., "%", "MB")
- color : String (optional)         → Color for the gauge (default: "#2563eb")

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [75, 0, 100, "%", "#10b981"])}
-->

<#-- Fallbacks for optional arguments -->
<#assign _min = min?default(0)>
<#assign _max = max?default(100)>
<#assign _unit = unit?default("")>
<#assign _color = color?default("#2563eb")>

<#-- Calculate percentage for display -->
<#assign percentage = ((value - _min) / (_max - _min) * 100)?round>

@GemColumn(
  hAlign = "center",
  vAlign = "center",
  components = [
    @GemText(
      value = "${value}${_unit}",
      fontSize = "32px",
      fontWeight = "bold",
      color = "${_color}"
    ),
    @GemText(
      value = "${percentage}%",
      fontSize = "16px",
      color = "#6b7280"
    ),
    @GemText(
      value = "Range: ${_min} - ${_max}${_unit}",
      fontSize = "12px",
      color = "#9ca3af"
    )
  ]
);
