${tc.signature(data, innerRadius)}

<#--
pie-chart.ftl for metrics
Lightweight wrapper around <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> specifically for metrics display.

Parameters
- data        : GemPieChartTypes.GemPieChartData  → Prepared data object containing the slices.
- innerRadius : Number (optional)                 → Size of the doughnut hole; default = 50.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.pie-chart", [data, 40])}
-->

<#-- Fallback for optional argument -->
<#assign _inner = innerRadius?default(50)>

@Gem<PERSON>ie<PERSON><PERSON>(
    data        = ${data},
    innerRadius = ${_inner}
);
