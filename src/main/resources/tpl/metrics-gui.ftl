<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "domainPackage", "metrics")}
/* (c) https://github.com/MontiCore/monticore */
package ${domainPackage?lower_case};

//data classes
import ${domainPackage}.${name};

//GUI models
${tc.includeArgs("tpl.metrics.imports-metrics", [domainClass, name, metrics])}

page ${name}Metrics(${name} ${name?lower_case}) {
${tc.includeArgs("tpl.metrics.card-metrics", [domainClass, name, metrics])}
}